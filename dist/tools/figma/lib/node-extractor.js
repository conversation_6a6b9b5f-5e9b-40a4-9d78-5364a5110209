/**
 * Node extraction utilities for converting hierarchical nodes to downloadable image arrays
 */
/**
 * Extract downloadable nodes from a simplified design
 */
export function extractDownloadableNodes(design) {
    const downloadableNodes = [];
    const nodeMap = new Map();
    const parentMap = new Map();
    // Build node map and parent relationships
    function buildMaps(node, parent) {
        nodeMap.set(node.id, node);
        if (parent) {
            parentMap.set(node.id, parent);
        }
        if (node.children) {
            node.children.forEach(child => buildMaps(child, node));
        }
    }
    // Build maps for all nodes
    design.nodes.forEach(node => buildMaps(node));
    // Traverse and collect downloadable nodes
    function traverseNode(node) {
        // 1. 如果是图片，直接添加到下载数组中
        if (hasImageFill(node, design.globalVars)) {
            const downloadableNode = createDownloadableNode(node, design.globalVars);
            if (downloadableNode) {
                downloadableNodes.push(downloadableNode);
            }
        }
        // 2. 如果是VECTOR，判断父节点逻辑
        else if (node.type === "VECTOR" || node.type === "IMAGE-SVG") {
            const parent = parentMap.get(node.id);
            if (parent && shouldDownloadParentForVector(parent, design.globalVars)) {
                const downloadableNode = createDownloadableNode(parent, design.globalVars);
                if (downloadableNode) {
                    downloadableNodes.push(downloadableNode);
                }
            }
            else {
                // 如果父节点不符合条件，下载VECTOR本身
                const downloadableNode = createDownloadableNode(node, design.globalVars);
                if (downloadableNode) {
                    downloadableNodes.push(downloadableNode);
                }
            }
        }
        // Recursively traverse children
        if (node.children) {
            node.children.forEach(traverseNode);
        }
    }
    design.nodes.forEach(traverseNode);
    // 基于node的id去重
    const uniqueNodes = new Map();
    downloadableNodes.forEach(node => {
        uniqueNodes.set(node.nodeId, node);
    });
    return Array.from(uniqueNodes.values());
}
/**
 * Check if a node has image fill
 */
function hasImageFill(node, globalVars) {
    if (node.fills) {
        const fills = globalVars.styles[node.fills];
        if (Array.isArray(fills)) {
            return fills.some((fill) => fill.type === "IMAGE" && fill.imageRef);
        }
    }
    return false;
}
/**
 * Check if a node is a text node
 */
function isTextNode(node) {
    return node.type === "TEXT" || (node.text !== undefined && node.text !== "");
}
/**
 * Check if parent node should be downloaded for a VECTOR child
 * 判断父节点的子节点中，没有图片节点，没有文本节点
 */
function shouldDownloadParentForVector(parent, globalVars) {
    if (!parent.children || parent.children.length === 0) {
        return false;
    }
    // 检查父节点的所有子节点
    for (const child of parent.children) {
        // 如果有图片节点，不下载父节点
        if (hasImageFill(child, globalVars)) {
            return false;
        }
        // 如果有文本节点，不下载父节点
        if (isTextNode(child)) {
            return false;
        }
    }
    return true;
}
/**
 * Create a downloadable node object
 */
function createDownloadableNode(node, globalVars) {
    const baseNode = {
        nodeId: node.id,
        fileName: sanitizeFileName(node.name),
        nodeType: node.type,
        nodeName: node.name
    };
    // Check for image fills first (highest priority)
    if (hasImageFill(node, globalVars)) {
        const fills = globalVars.styles[node.fills];
        if (Array.isArray(fills)) {
            const imageFill = fills.find((fill) => fill.type === "IMAGE" && fill.imageRef);
            if (imageFill) {
                baseNode.imageRef = imageFill.imageRef;
                baseNode.fileName = ensureExtension(baseNode.fileName, "png");
                return baseNode;
            }
        }
    }
    // Vector nodes default to SVG
    if (node.type === "VECTOR" || node.type === "IMAGE-SVG") {
        baseNode.fileName = ensureExtension(baseNode.fileName, "svg");
        return baseNode;
    }
    // Other nodes (FRAME, GROUP, COMPONENT, INSTANCE) default to PNG
    if (["FRAME", "GROUP", "COMPONENT", "INSTANCE"].includes(node.type)) {
        baseNode.fileName = ensureExtension(baseNode.fileName, "png");
        return baseNode;
    }
    return null;
}
/**
 * Sanitize filename for safe file system usage
 */
function sanitizeFileName(name) {
    return name
        .replace(/[^a-zA-Z0-9\s\-_]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .toLowerCase()
        .substring(0, 50); // Limit length
}
/**
 * Ensure filename has the correct extension
 */
function ensureExtension(fileName, extension) {
    const hasExtension = fileName.toLowerCase().endsWith(`.${extension}`);
    return hasExtension ? fileName : `${fileName}.${extension}`;
}
//# sourceMappingURL=node-extractor.js.map